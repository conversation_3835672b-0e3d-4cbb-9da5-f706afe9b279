import BaseSimulate, { FeeConfig } from "../BaseSimulate";
import { CoinStruct, getFullnodeUrl, SuiClient } from "@mysten/sui/client";
import BigNumber from "bignumber.js";
import { TCoinMetadata, TPosition } from "@/types";
import { convertDecToMist, isZero, toStringBN } from "@/utils/helper";
import {
  getOwnerCoinOnchain,
  getReferenceGasPrice,
  suiClient,
} from "@/utils/suiClient";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import { AggregatorClient } from "@cetusprotocol/aggregator-sdk";
import { Transaction } from "@mysten/sui/transactions";

export default class FlowxAggregatorSimulate extends BaseSimulate {
  public buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    tokenIn: TCoinMetadata
  ) => {
    const client = new AggregatorClient({
      signer: walletAddress,
    });
    const amount = new BigNumber(toStringBN(exactAmountIn));
    const from = tokenIn.address;
    const target = tokenOut.address;

    const routers = await client.findRouters({
      from,
      target,
      amount,
      byAmountIn: true, // true means fix input amount, false means fix output amount
    });

    console.log("routers", routers);

    if (!routers) {
      throw new Error(
        `No routes found for tokenIn: ${from} and tokenOut: ${target}`
      );
    }

    const txb = new Transaction();
    await client.fastRouterSwap({
      router: routers,
      txb,
      slippage: 1,
    });

    txb.setSender(walletAddress);
    return { tx: txb, amountOut: routers.amountOut?.toString() };
  };

  private buildSellTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    coinObjs: (CoinStruct & { owner: string })[]
  ) => {
    const client = new AggregatorClient({ signer: walletAddress });
    const amount = new BigNumber(toStringBN(exactAmountIn));
    const from = tokenIn.address;
    const target = tokenOut.address;

    const routers = await client.findRouters({
      from,
      target,
      amount,
      byAmountIn: true,
    });

    if (!routers) {
      throw new Error(
        `No routes found for tokenIn: ${from} and tokenOut: ${target}`
      );
    }

    const txb = new Transaction();
    await client.fastRouterSwap({
      router: routers,
      txb,
      slippage: 1,
    });
    txb.setSender(walletAddress);

    return { tx: txb, amountOut: routers.amountOut?.toString() };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined
  ) => {
    return this.buildBuyTransaction(walletAddress, amountIn, tokenOut, tokenIn);
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint,
    feeConfig?: FeeConfig
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    // If no fee config, use the original behavior
    if (!feeConfig || feeConfig.feeRate === 0) {
      return this.buildSellTransaction(
        walletAddress,
        exactAmountIn,
        tokenIn,
        tokenOut,
        coinObjs
      );
    }

    // Calculate fee amount
    const feeAmount = currentAmount
      .multipliedBy(feeConfig.feeRate)
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    // Build transaction with both sell and fee transfer
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    // Merge all token coins if there are multiple
    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    // Split coins for sell amount and fee amount
    const coinsToSplit = [];
    if (exactAmountIn.isGreaterThan(0)) {
      coinsToSplit.push(tx.pure.u64(exactAmountIn.toFixed()));
    }
    if (feeAmount.isGreaterThan(0)) {
      coinsToSplit.push(tx.pure.u64(feeAmount.toFixed()));
    }

    const splitCoins = tx.splitCoins(
      tx.object(coinObjs[0].coinObjectId),
      coinsToSplit
    );

    // Build the Cetus aggregator sell transaction manually
    // This is a simplified version - you may need to adjust based on actual Cetus aggregator implementation
    const sellResult = await this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenIn,
      tokenOut,
      coinObjs
    );

    // Extract and modify the commands to use our split coin
    const sellCommands = sellResult.tx.getData().commands;
    sellCommands.forEach((command) => {
      if (command.$kind === "MoveCall") {
        const moveCall = command.MoveCall;
        // Replace coin references with our split coin where appropriate
        const modifiedArgs = moveCall.arguments.map((arg, index) => {
          // This is a heuristic - you may need to adjust based on the specific moveCall structure
          if (index === moveCall.arguments.length - 2) {
            // Usually the coin is second to last
            return splitCoins[0];
          }
          return arg;
        });

        tx.moveCall({
          target: `${moveCall.package}::${moveCall.module}::${moveCall.function}`,
          typeArguments: moveCall.typeArguments,
          arguments: modifiedArgs,
        });
      }
    });

    // Transfer the fee if there is one
    if (feeAmount.isGreaterThan(0) && splitCoins.length > 1) {
      tx.transferObjects(
        [splitCoins[1]], // The fee coin
        tx.pure.address(feeConfig.feeRecipientAddress)
      );
    }

    return { tx, amountOut: sellResult.amountOut };
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      position.tokenQuote,
      tokenCoinObjects
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
