import { CoinStruct } from "@mysten/sui/client";
import { Transaction } from "@mysten/sui/transactions";
import BigNumber from "bignumber.js";
import { TCoinMetadata, TPosition } from "@/types";
import { isZero } from "@/utils/helper";
import { getOwnerCoinOnchain, getReferenceGasPrice } from "@/utils/suiClient";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import BaseSimulate, { FeeConfig } from "../BaseSimulate";

export const FLOWX_PACKAGE =
  "0xbab579951c9d9fdefbbe34cbdfbbef843a80a6c83ef5f852f67dcd02147ecd99";
export const FLOWX_MODULE = "flow_x_router";
export const FLOWX_FEE_OBJECT_ID =
  "0x9a57d1f6fdff284d085b3a00afcf22031b2cb5981b9502b64c225d5ccf9d1db4";
export const FLOWX_CONFIG_OBJECT_ID =
  "0xb65dcbf63fd3ad5d0ebfbf334780dc9f785eff38a4459e37ab08fa79576ee511";

export default class FlowxSimulate extends BaseSimulate {
  private buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    gasBasePrice: bigint
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    const [coin] = tx.splitCoins(tx.gas, [
      tx.pure.u64(exactAmountIn.toString()),
    ]);

    tx.moveCall({
      target: `${FLOWX_PACKAGE}::${FLOWX_MODULE}::buy_exact_in`, // package
      typeArguments: [
        tokenOut.address, // tokenAddress
      ],
      arguments: [
        tx.object(
          FLOWX_FEE_OBJECT_ID // feeObject
        ),
        tx.object(
          FLOWX_CONFIG_OBJECT_ID // container
        ),
        tx.pure.u64(exactAmountIn.toString()), // amountIn
        tx.pure.u64(0), // amountOutMin
        coin,
        tx.pure.string("abc"), // orderId
      ],
    });

    return { tx, amountOut: NaN };
  };

  private buildSellTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber,
    tokenIn: TCoinMetadata,
    gasBasePrice: bigint,
    coinObjs: (CoinStruct & { owner: string })[]
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    tx.moveCall({
      target: `${FLOWX_PACKAGE}::${FLOWX_MODULE}::sell_exact_in`, // package
      typeArguments: [
        tokenIn.address, // tokenAddress
      ],
      arguments: [
        tx.object(
          FLOWX_FEE_OBJECT_ID // feeObject
        ),
        tx.object(
          FLOWX_CONFIG_OBJECT_ID // container
        ),
        tx.pure.u64(exactAmountIn.toString()), // amountIn
        tx.pure.u64(0), // amountOutMin
        tx.object(coinObjs[0].coinObjectId), // tokenInObject
        tx.pure.string("abc"), // orderId
      ],
    });

    return { tx, amountOut: NaN };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      tokenOut,
      gasBasePrice
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint,
    feeConfig?: FeeConfig
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    // If no fee config, use the original behavior
    if (!feeConfig || feeConfig.feeRate === 0) {
      return this.buildSellTransaction(
        walletAddress,
        exactAmountIn,
        tokenIn,
        gasBasePrice,
        coinObjs
      );
    }

    // Calculate fee amount
    const feeAmount = currentAmount
      .multipliedBy(feeConfig.feeRate)
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    // Build transaction with both sell and fee transfer
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    // Merge all token coins if there are multiple
    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    // Split coins for sell amount and fee amount
    const coinsToSplit = [];
    if (exactAmountIn.isGreaterThan(0)) {
      coinsToSplit.push(tx.pure.u64(exactAmountIn.toFixed()));
    }
    if (feeAmount.isGreaterThan(0)) {
      coinsToSplit.push(tx.pure.u64(feeAmount.toFixed()));
    }

    const splitCoins = tx.splitCoins(
      tx.object(coinObjs[0].coinObjectId),
      coinsToSplit
    );

    // Add the DEX sell transaction using the split coin
    tx.moveCall({
      target: `${FLOWX_PACKAGE}::${FLOWX_MODULE}::sell_exact_in`,
      typeArguments: [tokenIn.address],
      arguments: [
        tx.object(FLOWX_FEE_OBJECT_ID),
        tx.object(FLOWX_CONFIG_OBJECT_ID),
        tx.pure.u64(exactAmountIn.toString()),
        tx.pure.u64(0), // amountOutMin
        splitCoins[0], // Use the split coin for selling
        tx.pure.string("abc"), // orderId
      ],
    });

    // Transfer the fee if there is one
    if (feeAmount.isGreaterThan(0) && splitCoins.length > 1) {
      tx.transferObjects(
        [splitCoins[1]], // The fee coin
        tx.pure.address(feeConfig.feeRecipientAddress)
      );
    }

    return { tx, amountOut: NaN };
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      gasBasePrice,
      tokenCoinObjects
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
