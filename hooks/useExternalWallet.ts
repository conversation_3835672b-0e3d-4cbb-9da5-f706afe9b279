import { useCallback } from "react";
import { useCurrentAccount } from "@mysten/dapp-kit";
import { TPair } from "@/types";
import DexesFactory from "../utils/simulates/dexes/DexesFactory";
import { Transaction } from "@mysten/sui/transactions";
import { getReferenceGasPrice, getOwnerCoinOnchain } from "../utils/suiClient";
import { convertDecToMist } from "../utils/helper";
import { SUI_DECIMALS, SUI_TOKEN_ADDRESS_SHORT } from "../utils/contants";
import useProcessTx from "./useProcessTx";
import { toastError } from "../libs/toast";
import { EDex } from "../enums/dex.enum";
import BigNumber from "bignumber.js";
import config from "@/config";

export const useExternalWallet = () => {
  const currentAccount = useCurrentAccount();
  const { processTx } = useProcessTx();

  const onBuyToken = useCallback(
    async (
      pair: TPair,
      buyAmount: string,
      setIsLoading: (isLoading: boolean) => void,
      onSuccess?: (digest?: string) => void
    ) => {
      try {
        if (!currentAccount?.address) {
          return;
        }

        const pool = {
          dex: pair?.dex.dex as any,
          objectId: pair?.poolId,
        };

        setIsLoading(true);
        const instance = DexesFactory.getDexesInstance(pair?.dex?.dex as EDex);
        const gasBasePrice = await getReferenceGasPrice();
        const exactAmountIn = convertDecToMist(buyAmount, SUI_DECIMALS);
        let output: { tx: Transaction; amountOut: string | number } | null =
          null;
        const raidenxFee = BigNumber(exactAmountIn)
          .times(config.raidenxFeeRate)
          .toFixed();
        const amountDeductedForRaidenxFee = BigNumber(exactAmountIn)
          .minus(raidenxFee)
          .toFixed();

        if (pool.dex === EDex.MOVEPUMP || pool.dex === EDex.MOONBAGS) {
          output = await instance.buildBuyTransaction(
            currentAccount.address,
            amountDeductedForRaidenxFee,
            pair?.tokenBase,
            pair?.tokenQuote,
            pool?.objectId,
            gasBasePrice
          );
        } else if (pool.dex === EDex.SUIAIFUN) {
          output = await instance.extractBaseTokenOut(
            currentAccount.address,
            amountDeductedForRaidenxFee,
            pair?.tokenQuote,
            pair?.tokenBase,
            pool.objectId,
            gasBasePrice,
            true // isBuyBySuiToken
          );
        } else if (pool.dex === EDex.STEAM) {
          output = await instance.extractBaseTokenOut(
            currentAccount.address,
            amountDeductedForRaidenxFee,
            pair?.tokenQuote,
            pair?.tokenBase,
            pair.isXQuoteToken,
            pair.feeTier,
            pool.objectId,
            gasBasePrice
          );
        } else {
          output = await instance.extractBaseTokenOut(
            currentAccount.address,
            amountDeductedForRaidenxFee,
            pair?.tokenQuote,
            pair?.tokenBase,
            pool.objectId,
            gasBasePrice
          );
        }

        if (!output?.tx) {
          throw new Error("Failed to build transaction");
        }

        const [raidenxFeeCoin] = output.tx.splitCoins(output.tx.gas, [
          output.tx.pure.u64(raidenxFee),
        ]);
        output.tx.transferObjects(
          [raidenxFeeCoin],
          output.tx.pure.address(config.raidenxAddress)
        );

        await processTx(output.tx, onSuccess);
      } catch (error: any) {
        console.log(error, "onBuyToken error");
        setIsLoading(false);
        toastError("Error ", error?.message || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const onSellToken = useCallback(
    async (
      pair: TPair,
      sellPercent: string,
      setIsLoading: (isLoading: boolean) => void,
      onSuccess?: (digest?: string) => void
    ) => {
      try {
        if (!currentAccount?.address) {
          return;
        }

        const pool = {
          dex: pair.dex.dex as any,
          objectId: pair.poolId,
        };

        setIsLoading(true);
        const instance = DexesFactory.getDexesInstance(pair?.dex?.dex as EDex);
        const gasBasePrice = await getReferenceGasPrice();
        let output: { tx: Transaction; amountOut: string | number } | null =
          null;

        const feeRate = config.raidenxFeeRate; // 0.01 = 1%
        const sellPercentFloat = parseFloat(sellPercent);
        const actualSellPercent = sellPercentFloat * (1 - feeRate);
        const feePercent = sellPercentFloat * feeRate;

        console.log(
          `Original: ${sellPercent}%, Actual sell: ${actualSellPercent.toFixed(
            6
          )}%, Fee: ${feePercent.toFixed(6)}%`
        );

        // First, extract the fee BEFORE building the sell transaction
        let feeAmount = BigNumber(0);
        if (feePercent > 0) {
          const [coinObjs, currentTokenBalance] = await getOwnerCoinOnchain(
            currentAccount.address,
            pair?.tokenBase?.address
          );

          if (currentTokenBalance.isZero()) {
            throw new Error("Insufficient balance");
          }

          feeAmount = currentTokenBalance
            .multipliedBy(feePercent)
            .div(100)
            .integerValue(BigNumber.ROUND_FLOOR);

          // Create a separate transaction to extract the fee first
          if (feeAmount.isGreaterThan(0) && coinObjs.length > 0) {
            const feeTx = new Transaction();
            feeTx.setGasBudget(********);
            feeTx.setSender(currentAccount.address);
            feeTx.setGasPrice(gasBasePrice);

            // Merge all token coins if there are multiple
            if (coinObjs.length > 1) {
              feeTx.mergeCoins(
                coinObjs[0].coinObjectId,
                coinObjs.slice(1).map((coin) => coin.coinObjectId)
              );
            }

            // Split the fee amount from the token coins
            const [raidenxFeeCoin] = feeTx.splitCoins(
              feeTx.object(coinObjs[0].coinObjectId),
              [feeTx.pure.u64(feeAmount.toFixed())]
            );

            // Transfer the fee
            feeTx.transferObjects(
              [raidenxFeeCoin],
              feeTx.pure.address(config.raidenxAddress)
            );

            // Execute the fee transaction first
            await processTx(feeTx);

            // Wait a moment for the transaction to be processed
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }

        // Now get the sell transaction for the adjusted amount
        output = await instance.extractQuoteTokenOut(
          currentAccount.address,
          pair?.tokenBase,
          pair?.tokenQuote,
          actualSellPercent.toString(),
          pool.objectId,
          gasBasePrice
        );

        if (!output?.tx) {
          throw new Error("Failed to build sell transaction");
        }

        await processTx(output.tx, onSuccess);
      } catch (error: any) {
        console.log(error, "error");
        setIsLoading(false);
        toastError("Error ", error?.message || "Something went wrong");
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  return {
    onBuyToken,
    onSellToken,
  };
};
